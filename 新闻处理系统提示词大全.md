# 新闻处理系统提示词大全

## 📋 目录

1. [标题生成提示词](#标题生成提示词)
2. [文章写作提示词](#文章写作提示词)
3. [话题合并提示词](#话题合并提示词)
4. [新闻搜索提示词](#新闻搜索提示词)
5. [要点总结提示词](#要点总结提示词)
6. [API配置参数](#api配置参数)

---

## 🎯 标题生成提示词

### 1. 虎扑风格标题生成策略

**用途**: 生成具有强烈吸引力的文章标题
**位置**: `database_topic_merger.py` - `generate_engaging_article()` 函数内
**参数设置**: 
- `temperature`: 0.3
- `max_tokens`: 16384
- `model`: "DeepSeek-V3-Fast"

```
### 2. 标题策略（10-18字）
- **强钩子优先**：标题必须有吸引力，让人忍不住点开
- **钩子技巧**：
  * 引用式："XXX，下赛季我罩着你"
  * 反转式："手术成功！哈利伯顿：即使跟腱撕裂也不后悔"
  * 悬念式："这个决定，改变了整个联盟格局"
  * 对比式："从替补到核心，他只用了一个赛季"
  * 冲突式："球迷怒了：这笔交易简直是抢劫"
- **情感词汇**：适当使用"震撼"、"意外"、"逆转"、"爆发"等词
- **数字冲击**：用具体数字增强冲击力

**🔥 特别强调：标题是文章成功的关键！**
- 标题必须有强烈的钩子，让读者产生"这是怎么回事？"的好奇心
- 可以用引用、反转、悬念、对比、冲突等手法
- 要让人看到标题就忍不住想点开
```

---

## ✍️ 文章写作提示词

### 1. 虎扑风格深度文章生成

**用途**: 生成客观、流畅、有深度的新闻文章
**位置**: `database_topic_merger.py` - `generate_engaging_article()` 函数
**风格特点**: 第三人称客观叙述，类似虎扑体育新闻风格

```
你是虎扑体育的资深编辑，擅长写出客观、流畅、有深度的体育新闻文章。

请基于以下新闻要点，围绕事件"{topic}"写一篇虎扑风格的深度文章。

**核心事件**: {topic}

**新闻要点 (已确认全部与核心事件相关)**:
{news_points}

## 🚨 核心写作纪律：
**本文必须且只能围绕"{topic}"这一个核心事件展开。** 严禁发散到其他不相关的人物或事件上。你的任务是把这一个事件讲深、讲透，而不是做一个新闻盘点。从第一个字到最后一个字，都必须服务于这个核心事件。

## 虎扑风格写作要求：

### 1. 文章定位
- **客观叙述**：以第三人称视角，客观描述事件和人物
- **深度挖掘**：不只是表面新闻，要挖掘背后的故事和细节
- **专业分析**：基于事实进行合理分析，有理有据

### 3. 文章结构（总计1000-1500字）

**开头段（150-200字）- 场景描述**
- 用具体的时间、地点、人物开场
- 描述一个具体的场景或事件
- 如果内容适合，可以用有趣的细节或对比作为钩子
- 语言平实，但可以适当增加吸引力
- 例如："2025年6月29日，这是被开拓者选中的杨瀚森第一天到球队报道的日子。"

**主体部分（700-1100字）- 层层展开**
- **🚨 绝对不要用（1）（2）（3）分点！**
- 按时间线或逻辑线自然展开
- 多用具体的细节和数据
- 引用当事人的话语和行为
- 挖掘事件背后的深层原因
- 适当使用对比、反转等手法增加可读性
- 语言客观，但可以有适度的叙述技巧

**结尾段（150-200字）- 升华总结**
- 总结事件的意义或影响
- 可以适当展望未来
- 语调平和，不过分煽情
- 让读者对事件有更深理解

### 4. 写作技巧

**客观叙述**：
- 以第三人称视角描述，避免"我觉得"、"我认为"
- 用事实说话，让读者自己得出结论
- 保持中性立场，不带个人情感色彩

**细节刻画**：
- 用具体的时间、地点、数据增强真实感
- 描述人物的具体行为和表情
- 引用原话时要准确，增加可信度

**逻辑连贯**：
- 段落之间要有自然的过渡
- 按照时间线或因果关系组织内容
- 每个段落都要为主题服务

### 5. 语言风格
- **平实客观**：语言朴实，不华丽不煽情
- **专业准确**：体育术语使用准确，数据真实
- **流畅易读**：句子长短适中，逻辑清晰
- **有深度**：不只是表面信息，要有分析和思考

### 6. 🚨 绝对禁止（重要！）
- ❌ **使用（1）（2）（3）或第一、第二、第三等分点**
- ❌ **使用第一人称"我"、"我们"等**
- ❌ **过度煽情或主观评判**
- ❌ **使用网络流行语或过于口语化表达**
- ❌ **夸大事实或添加不实信息**

### 7. 虎扑风格检查清单
✅ **标题必须有强钩子**，让人忍不住想点开
✅ 开头用具体场景描述，有画面感
✅ 全文第三人称客观叙述
✅ 多用具体细节和准确数据
✅ 逻辑清晰，段落间自然过渡
✅ 语言平实专业，适当使用叙述技巧增加可读性
✅ 在保持客观的前提下，让文章有吸引力

**🎯 最终要求**：写出一篇像虎扑编辑写的专业体育文章，客观、深度、有料，标题要有强烈的点击欲望！

请创作文章：
```

---

## 🔄 话题合并提示词

### 1. 大力度话题合并策略

**用途**: 将大量新闻标题合并成少数核心话题
**位置**: `database_topic_merger.py` - `_process_topic_batch()` 函数
**目标**: 输入50个标题，输出15-20个话题，压缩率60-70%

```
你是一名资深的新闻编辑，请对以下新闻标题进行大力度合并分类，目标是将50个标题合并成15-20个话题。

新闻标题列表：
{titles_text}

## 🚨 核心要求：大力度合并，话题名称6-10字！

### 1. 强力合并策略

**✅ 必须大力合并**：
- **同行业必合并**：特斯拉 + 小米汽车 + 比亚迪 + 理想 + 蔚来 → "新能源汽车"
- **同领域必合并**：苹果 + 华为 + 小米手机 + OPPO + vivo → "智能手机"
- **同类型必合并**：抖音 + 快手 + 小红书 → "短视频平台"
- **同公司必合并**：小米手机 + 小米汽车 + 雷军 → "小米动态"
- **同人物必合并**：张学友演唱会 + 张学友新歌 → "张学友动态"
- **同事件必合并**：NBA选秀 + NBA转会 + NBA赛事 → "NBA动态"

### 2. 行业合并标准

**科技类**：
- 新能源汽车：特斯拉、小米汽车、比亚迪、理想、蔚来
- 智能手机：苹果、华为、小米、OPPO、vivo
- AI科技：OpenAI、百度AI、阿里AI、腾讯AI
- 电商平台：淘宝、京东、拼多多

**娱乐类**：
- 短视频：抖音、快手、小红书
- 影视剧：各种电影、电视剧、综艺
- 音乐：各种歌手、演唱会、新歌

**体育类**：
- 篮球：NBA、CBA、各种篮球新闻
- 足球：中超、英超、世界杯、各种足球新闻

**政治类**：
- 中美关系：所有中美相关新闻
- 中东局势：伊朗、以色列、巴勒斯坦相关
- 俄乌冲突：所有俄乌战争相关

### 3. 话题命名要求
- **长度**：严格6-10字
- **格式**：[行业/领域] 或 [公司名] 或 [人物名]
- **示例**：
  - "新能源汽车" (5字) ✅
  - "智能手机" (4字) ✅
  - "小米动态" (4字) ✅
  - "NBA动态" (5字) ✅
  - "中美关系" (4字) ✅

### 4. 合并目标
- 输入50个标题，输出15-20个话题
- 压缩率要达到60-70%
- 每个话题要包含多条相关新闻

**🎯 重要提醒**：
1. 必须大力度合并，不要保守
2. 同行业、同领域的必须合并
3. 话题数量控制在15-20个
4. 每个话题名称6-10字

请严格按照以上要求输出话题列表，每行一个话题，不要编号：
```

### 2. 事件驱动最终合并

**用途**: 将话题进一步精炼为具体新闻事件
**位置**: `database_topic_merger.py` - `_final_merge_batch()` 函数
**特点**: 事件驱动，避免过度合并

```
你是一名顶级新闻主编，现在需要从以下话题中提炼出最有价值的"新闻事件"。

话题列表：
{topics_text}

## 🚨 核心任务：识别"新闻事件"，而非"合并话题"！

### 1. "新闻事件"的定义：
- 必须是一个具体的、已发生或正在发生的独立事件
- 必须有清晰的主体和动作
- 事件名称应高度概括，本身就是一条微新闻

### 2. 事件提炼原则：

**✅ 精准提炼 (DO):**
- **同一事件，不同侧面 -> 提炼为同一事件**
  - "小米SU7开启交付" + "雷军回应小米汽车定价" + "首批小米SU7车主谈体验"
  - **✅ 提炼为 -> "小米SU7上市交付与市场反响"**
- **同一主体，不同事件 -> 保持为不同事件**
  - "苹果发布iOS 18" + "苹果Vision Pro销量下滑"
  - **✅ 提炼为 -> "苹果发布iOS 18系统" 和 "苹果Vision Pro销量遇冷" (这是两个独立事件!)**

**❌ 严禁过度合并 (DON'T):**
- **不同主体，同一领域 -> 绝对禁止合并！**
  - "小米SU7发布" + "特斯拉Model Y降价"
  - **❌ 错误合并 -> "新能源汽车市场动态" (这是无效话题!)**
- **抽象、宽泛、无具体事件 -> 彻底抛弃！**
  - "科技行业展望"、"手机市场分析" (这些都是没有报道价值的空洞话题)

### 3. 事件命名标准 (6-15字):
- 格式：[主体] + [核心事件/动作] + [结果/影响]
- 示例：
  - ✅ "英伟达发布新一代AI芯片"
  - ✅ "OpenAI CEO奥特曼访问中国"
  - ✅ "NBA独行侠队高薪续约欧文"
  - ✅ "虐猫考生被南京大学拒录"

### 4. 输出要求：
- 从给定的标题中，提炼出大约10-15个最重要、最具体的"新闻事件"
- 如果一个标题无法归入任何具体事件，直接丢弃
- 每行输出一个"新闻事件"，不要编号
- 事件名称6-15字以内

**🎯 你的价值在于发现"金矿"（具体事件），而不是把所有石头（标题）都混在一起。开始工作。**

请严格按照以上标准输出新闻事件列表，每行一个事件，不要编号：
```

---

## 🔍 新闻搜索提示词

### 1. 锚点问题生成

**用途**: 为新闻事件生成精确搜索问题
**位置**: `database_topic_merger.py` - `_generate_anchor_question()` 函数

```
你是一个信息检索专家。请将以下新闻事件，转换成一个用于在向量数据库中进行精确搜索的核心问题。

新闻事件：{event}

要求：
- 问题要具体但不过于复杂，便于匹配新闻内容
- 包含事件的核心关键词
- 语言自然，像真实用户的搜索需求

示例：
- 事件: "小米SU7上市交付与市场反响"
- 问题: "小米SU7汽车上市交付情况和用户反馈如何？"
- 事件: "独行侠高薪续约欧文"
- 问题: "独行侠队与欧文续约的具体情况和合同细节是什么？"
- 事件: "习近平总书记重要讲话"
- 问题: "习近平总书记最新重要讲话的主要内容和核心观点是什么？"

现在，请为以下事件生成核心问题：
新闻事件：{event}

只输出生成的问题，不要其他内容。
```

### 2. 智能关键词生成

**用途**: 为话题生成最佳搜索关键词
**位置**: `database_topic_merger.py` - `_generate_smart_keywords_with_llm()` 函数

```
你是一名专业的信息检索专家，请为以下话题生成最佳的搜索关键词。

话题：{topic}

## 任务要求：
必须生成恰好3个搜索关键词，不能多也不能少！确保能在新闻数据库中找到相关内容。

## 关键词生成策略：

### 1. 核心实体提取
- 提取话题中的核心人物、公司、产品、地点
- 例如："小米汽车动态" → 提取"小米"、"汽车"

### 2. 关键词层次化
- **核心词**：最重要的主体（如：小米、特斯拉、苹果）
- **领域词**：所属行业（如：汽车、手机、科技）
- **事件词**：具体事件（如：发布、降价、财报）

### 3. 搜索优化
- 使用常见的新闻用词
- 避免过于抽象的概念
- 确保关键词在新闻标题中常见

### 4. 示例参考：

**话题**："小米汽车动态"
**关键词**：
- 小米
- 汽车
- 小米汽车
- 雷军
- 新能源

**话题**："特斯拉降价风波"
**关键词**：
- 特斯拉
- 降价
- 马斯克
- 电动车
- 价格

**话题**："NBA选秀大会"
**关键词**：
- NBA
- 选秀
- 篮球
- 球员
- 新秀

## 输出格式：
请直接输出3个关键词，每行一个，不要编号或其他格式：
```

---

## 📋 要点总结提示词

### 1. 新闻要点总结

**用途**: 从相关新闻中提取核心要点
**位置**: `database_topic_merger.py` - `summarize_news_points()` 函数

```
请基于以下新闻内容，总结出关于话题 "{topic}" 的核心要点。

话题: {topic}

相关新闻:
{news_content}

要求:
1. 提取与话题最相关的核心信息和关键事实
2. 按重要性排序，列出5-8个要点
3. 每个要点包含：事实描述、背景信息、影响分析
4. 识别争议点、热点、趋势和深层原因
5. 保持客观准确，但要挖掘新闻背后的故事
6. 为后续文章创作提供丰富素材

请输出要点总结：
```

---

## ⚙️ API配置参数

### LLM API调用参数

**配置文件**: `config.py` 和 `database_topic_merger.py`

```python
# API基础配置
{
    "api_keys": ["key1", "key2", "key3", ...],  # 支持多API key轮换
    "base_url": "https://www.sophnet.com/api/open-apis/chat/completions",
    "model": "DeepSeek-V3-Fast"
}

# 调用参数
{
    "model": "DeepSeek-V3-Fast",
    "messages": [{"role": "user", "content": prompt}],
    "max_tokens": 16384,
    "temperature": 0.3,
    "timeout": 120  # 2分钟超时
}
```

### 多线程配置

- **并发数**: 根据API key数量动态设置 (`max_workers = len(api_keys)`)
- **线程安全**: 每个线程分配专用API key
- **延迟控制**: 每次API调用间隔500ms避免频率限制

### 向量数据库配置

```python
# Embedding API配置
{
    "api_key": "embedding_api_key",
    "base_url": "https://www.sophnet.com/api/open-apis/projects/{project_id}/easyllms/embeddings",
    "project_id": "project_id",
    "easyllm_id": "easyllm_id",
    "dimensions": 768,
    "rate_limit": 60
}

# 搜索参数
{
    "top_k": 30,        # 最大搜索结果数
    "threshold": 0.4    # 相似度阈值
}
```

### 3. 搜索结果验证

**用途**: 验证搜索结果是否与核心事件相关
**位置**: `database_topic_merger.py` - `_validate_and_filter_results()` 函数

```
主编，请检查以下新闻标题列表，是否都严格围绕核心事件"{event}"展开。

核心事件：{event}

新闻标题列表：
{titles_text}

要求：
1. 逐一检查每个标题是否与核心事件直接相关
2. 如果标题与核心事件高度相关，输出 true
3. 如果标题与核心事件无关或相关性很低，输出 false
4. 严格标准：只有直接涉及该事件的新闻才算相关

输出格式：JSON数组，每个元素对应一个标题的判断结果
示例：[true, false, true, false, true]

请输出判断结果：
```

---

## 🎨 提示词分类总结

### 按功能分类

| 功能类别 | 提示词数量 | 主要用途 |
|---------|-----------|----------|
| **话题处理** | 3个 | 话题合并、事件提炼、最终合并 |
| **内容生成** | 2个 | 文章写作、要点总结 |
| **搜索优化** | 3个 | 关键词生成、锚点问题、结果验证 |

### 按处理阶段分类

| 处理阶段 | 相关提示词 | 输入 | 输出 |
|---------|-----------|------|------|
| **第一阶段** | 话题合并 | 50个新闻标题 | 15-20个话题 |
| **第二阶段** | 事件提炼 | 15-20个话题 | 10-15个具体事件 |
| **第三阶段** | 新闻搜索 | 事件名称 | 相关新闻列表 |
| **第四阶段** | 要点总结 | 相关新闻 | 核心要点 |
| **第五阶段** | 文章生成 | 事件+要点 | 完整文章 |

---

## 🔧 提示词优化建议

### 1. 标题吸引力优化
- **当前策略**: 使用钩子技巧（引用、反转、悬念、对比、冲突）
- **优化方向**: 可以增加更多情感词汇库，如"震撼"、"意外"、"逆转"、"爆发"
- **效果评估**: 通过点击率数据反馈调整钩子策略

### 2. 文章质量提升
- **当前优势**: 虎扑风格客观叙述，避免主观色彩
- **改进空间**: 可以增加更多具体的叙述技巧示例
- **质量控制**: 严格禁止分点式写作，保持自然流畅

### 3. 话题合并精度
- **合并策略**: 大力度合并同行业、同领域内容
- **精度控制**: 事件驱动避免过度合并
- **压缩目标**: 保持60-70%的压缩率

---

## 📊 API性能参数

### 推荐配置

```python
# 高质量文章生成配置
ARTICLE_GENERATION = {
    "temperature": 0.3,      # 保证输出稳定性
    "max_tokens": 16384,     # 支持长文章
    "timeout": 120,          # 2分钟超时
    "retry_count": 3         # 最多重试3次
}

# 快速话题处理配置
TOPIC_PROCESSING = {
    "temperature": 0.3,      # 保证分类准确性
    "max_tokens": 4096,      # 中等长度输出
    "timeout": 60,           # 1分钟超时
    "retry_count": 2         # 最多重试2次
}

# 搜索关键词生成配置
KEYWORD_GENERATION = {
    "temperature": 0.2,      # 更高准确性
    "max_tokens": 1024,      # 短输出
    "timeout": 30,           # 30秒超时
    "retry_count": 2         # 最多重试2次
}
```

### 并发处理策略

- **线程数**: 等于API key数量，通常3-6个
- **负载均衡**: 每个线程分配专用API key
- **错误恢复**: 多重备用解析策略
- **频率控制**: 每次调用间隔500ms

---

## 📝 使用说明

1. **标题生成**: 所有标题生成都集成在文章写作提示词中，通过"标题策略"部分实现
2. **风格一致性**: 所有提示词都遵循虎扑体育新闻风格，强调客观、专业、有深度
3. **参数调优**: `temperature=0.3` 保证输出稳定性，`max_tokens=16384` 支持长文本生成
4. **多线程支持**: 所有LLM调用都支持多线程并发，每个线程使用独立API key
5. **错误处理**: 包含多重备用策略，确保系统稳定运行
6. **质量控制**: 通过多层验证确保内容质量和相关性
7. **事件驱动**: 采用事件驱动方法避免话题过度合并，保持内容聚焦

---

## 🎯 核心设计理念

1. **客观性优先**: 所有提示词都强调第三人称客观叙述，避免主观色彩
2. **吸引力平衡**: 在保持客观的前提下，通过标题钩子和叙述技巧增加可读性
3. **深度挖掘**: 不满足于表面信息，要求挖掘新闻背后的故事和深层原因
4. **事件聚焦**: 每篇文章只围绕一个核心事件展开，避免内容发散
5. **专业标准**: 遵循新闻写作规范，使用准确的术语和数据

---

*最后更新: 2025年7月8日*
*文档版本: v1.0*
*适用系统: 自动化新闻处理流水线*

# 向量化数据库技术文档

## 📋 目录

1. [系统概述](#系统概述)
2. [API配置](#api配置)
3. [核心类介绍](#核心类介绍)
4. [向量化流程](#向量化流程)
5. [数据库操作](#数据库操作)
6. [搜索功能](#搜索功能)
7. [存储结构](#存储结构)
8. [使用示例](#使用示例)
9. [性能优化](#性能优化)
10. [故障排除](#故障排除)

---

## 🎯 系统概述

### 架构设计

我们的向量化数据库系统采用混合存储架构：
- **向量数据**: 使用Pickle序列化存储在本地文件
- **元数据**: 使用SQLite数据库存储，支持复杂查询
- **索引**: 内存中维护分类、来源、时间等多维索引
- **搜索**: 基于余弦相似度的向量检索

### 核心特性

- ✅ **高效存储**: 768维向量，支持数万条新闻
- ✅ **快速检索**: 余弦相似度搜索，毫秒级响应
- ✅ **多维过滤**: 支持分类、来源、时间、质量分数过滤
- ✅ **批量操作**: 支持批量添加和搜索
- ✅ **线程安全**: 支持多线程并发操作
- ✅ **持久化**: 自动保存和加载数据

---

## 🔑 API配置

### 环境变量配置

在 `.env` 文件中配置以下参数：

```bash
# Embedding API配置
EMBEDDING_API_KEY=your_embedding_api_key_here
EMBEDDING_PROJECT_ID=your_project_id_here
EMBEDDING_EASYLLM_ID=your_easyllm_id_here
```

### API配置详情

```python
# config.py 中的配置
EMBEDDING_CONFIG = {
    "api_key": "your_api_key",                    # SophNet API密钥
    "base_url": "https://www.sophnet.com/api/open-apis/projects/{project_id}/easyllms/embeddings",
    "project_id": "your_project_id",              # 项目ID
    "easyllm_id": "your_easyllm_id",             # EasyLLM服务ID
    "dimensions": 768,                            # 向量维度
    "rate_limit": 60                             # 每分钟请求限制
}
```

### API调用协议

#### 请求格式

```http
POST https://www.sophnet.com/api/open-apis/projects/{project_id}/easyllms/embeddings
Content-Type: application/json
Authorization: Bearer {api_key}

{
    "easyllm_id": "your_easyllm_id",
    "input_texts": ["文本1", "文本2", "文本3"],
    "dimensions": 768
}
```

#### 响应格式

```json
{
    "data": [
        {
            "embedding": [0.1, 0.2, 0.3, ...],  // 768维向量
            "index": 0
        },
        {
            "embedding": [0.4, 0.5, 0.6, ...],
            "index": 1
        }
    ]
}
```

#### 错误处理

```json
{
    "error": {
        "code": "RATE_LIMIT_EXCEEDED",
        "message": "请求频率超限"
    }
}
```

---

## 🏗️ 核心类介绍

### 1. VectorDatabase 类

**文件位置**: `vector_database.py`

```python
class VectorDatabase:
    """
    完整的向量数据库实现
    
    功能特性：
    - 高效的向量存储和检索
    - 支持元数据过滤
    - 持久化存储
    - 索引优化
    - 批量操作
    - 线程安全
    """
    
    def __init__(self, db_path: str = "vector_db", vector_dim: int = 768):
        """
        初始化向量数据库
        
        Args:
            db_path: 数据库存储路径
            vector_dim: 向量维度（默认768）
        """
```

#### 核心属性

```python
# 内存存储
self.vectors = {}          # {doc_id: numpy.array} 向量存储
self.metadata = {}         # {doc_id: dict} 元数据存储
self.doc_ids = []         # 文档ID列表

# 多维索引
self.category_index = defaultdict(list)  # 分类索引
self.source_index = defaultdict(list)    # 来源索引
self.time_index = defaultdict(list)      # 时间索引

# 线程安全
self.lock = threading.RLock()            # 读写锁

# 统计信息
self.stats = {
    'total_documents': 0,
    'total_searches': 0,
    'last_updated': None,
    'created_at': datetime.now().isoformat()
}
```

### 2. CSVNewsVectorProcessor 类

**文件位置**: `csv_to_vector_processor.py`

```python
class CSVNewsVectorProcessor:
    """CSV新闻向量化处理器"""
    
    def __init__(self, embedding_config, vector_db_path="csv_news_vectors"):
        """
        初始化处理器
        
        Args:
            embedding_config: Embedding API配置
            vector_db_path: 向量数据库存储路径
        """
```

#### 核心功能

- **数据加载**: 从CSV文件加载新闻数据
- **内容清洗**: 清理和标准化新闻内容
- **质量评估**: 评估新闻内容质量
- **向量化**: 调用API生成文本向量
- **批量处理**: 支持多线程批量处理
- **存储管理**: 自动存储到向量数据库

---

## 🔄 向量化流程

### 1. 数据预处理

```python
def clean_content(self, content: str) -> str:
    """清洗新闻内容"""
    # 1. 移除多余的换行和空格
    content = re.sub(r'\n+', '\n', content)
    content = re.sub(r'\s+', ' ', content)
    
    # 2. 移除特殊字符
    content = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\.,!?;:()（）"""]', '', content)
    
    # 3. 移除过短的句子
    sentences = content.split('。')
    valid_sentences = [s.strip() for s in sentences if len(s.strip()) > 10]
    
    return '。'.join(valid_sentences).strip()
```

### 2. 质量评估

```python
def evaluate_content_quality(self, title: str, content: str) -> float:
    """评估内容质量 (0.0-1.0)"""
    score = 0.0
    
    # 标题质量 (0-0.3)
    if title and len(title) > 5: score += 0.1
    if title and len(title) > 15: score += 0.1
    if title and not any(char in title for char in ['？', '！', '…']): score += 0.1
    
    # 内容长度 (0-0.4)
    if len(content) > 100: score += 0.1
    if len(content) > 300: score += 0.1
    if len(content) > 800: score += 0.1
    if len(content) > 1500: score += 0.1
    
    # 内容结构 (0-0.3)
    if '。' in content: score += 0.1
    if content.count('。') >= 3: score += 0.1
    if any(keyword in content for keyword in ['报道', '消息', '据悉', '记者']): score += 0.1
    
    return min(score, 1.0)
```

### 3. 向量生成

```python
def get_embeddings(self, texts: List[str], thread_id: str = "") -> Optional[List[List[float]]]:
    """获取文本向量（线程安全）"""
    # 1. 频率限制
    with self.request_lock:
        self._rate_limit_wait()
        
        # 2. 构建请求
        url = self.embedding_config['base_url'].format(
            project_id=self.embedding_config['project_id']
        )
        
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f"Bearer {self.embedding_config['api_key']}"
        }
        
        data = {
            "easyllm_id": self.embedding_config['easyllm_id'],
            "input_texts": texts,
            "dimensions": self.embedding_config['dimensions']
        }
        
        # 3. 发送请求
        response = self.session.post(url, headers=headers, json=data, timeout=30)
        response.raise_for_status()
        
        # 4. 解析响应
        result = response.json()
        embeddings = [item['embedding'] for item in result['data']]
        
        return embeddings
```

### 4. 批量处理流程

```python
def process_csv_to_vectors(self, csv_file_path: str, max_workers: int = 3, batch_size: int = 10):
    """多线程批量处理CSV新闻数据"""
    
    # 1. 加载CSV数据
    df = self.load_csv_data(csv_file_path)
    
    # 2. 转换为字典列表
    news_list = df.to_dict('records')
    
    # 3. 分批处理
    batches = [news_list[i:i+batch_size] for i in range(0, len(news_list), batch_size)]
    
    # 4. 多线程并发处理
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = []
        for i, batch in enumerate(batches, 1):
            future = executor.submit(
                self.process_news_batch, 
                batch, i, len(batches), f"T{i%max_workers+1}"
            )
            futures.append(future)
        
        # 5. 等待所有任务完成
        for future in as_completed(futures):
            try:
                future.result()
            except Exception as e:
                print(f"批次处理异常: {e}")
    
    # 6. 保存数据库
    self.vector_db.save_database()
```

---

## 💾 数据库操作

### 1. 添加文档

```python
# 单个文档
success = db.add_document(
    doc_id="news_001", 
    vector=embedding_vector,  # 768维向量
    metadata={
        'title': '新闻标题',
        'content': '新闻内容',
        'category': '科技',
        'source': '新浪新闻',
        'publish_time': '2025-01-01 12:00:00',
        'quality_score': 0.85
    }
)

# 批量添加
documents = [
    ("news_001", vector1, metadata1),
    ("news_002", vector2, metadata2),
    ("news_003", vector3, metadata3)
]
results = db.batch_add_documents(documents)
```

### 2. 搜索文档

```python
# 基础搜索
results = db.search(
    query_vector=query_embedding,
    top_k=10,                    # 返回前10个结果
    threshold=0.5                # 相似度阈值
)

# 带过滤条件的搜索
results = db.search(
    query_vector=query_embedding,
    top_k=10,
    threshold=0.5,
    filters={
        'category': '科技',       # 分类过滤
        'source': '新浪新闻',     # 来源过滤
        'date': '2025-01-01',     # 日期过滤
        'min_quality': 0.7        # 最低质量分数
    }
)
```

### 3. 文档管理

```python
# 获取文档
doc = db.get_document("news_001")

# 删除文档
success = db.delete_document("news_001")

# 获取统计信息
stats = db.get_stats()
db.print_stats()
```

---

## 🔍 搜索功能

### 1. 相似度计算

使用余弦相似度计算向量间的相似性：

```python
def search(self, query_vector, top_k=10, threshold=0.0, filters=None):
    """向量相似度搜索"""
    
    # 1. 归一化查询向量
    norm = np.linalg.norm(query_vector)
    if norm > 0:
        query_vector = query_vector / norm
    
    # 2. 获取候选文档
    candidate_ids = self._get_filtered_candidates(filters)
    
    # 3. 计算相似度
    similarities = []
    for doc_id in candidate_ids:
        if doc_id in self.vectors:
            # 余弦相似度 = 归一化向量的点积
            similarity = np.dot(query_vector, self.vectors[doc_id])
            if similarity >= threshold:
                similarities.append((doc_id, float(similarity)))
    
    # 4. 排序并返回结果
    similarities.sort(key=lambda x: x[1], reverse=True)
    return similarities[:top_k]
```

### 2. 多维过滤

```python
def _get_filtered_candidates(self, filters):
    """根据过滤条件获取候选文档"""
    if not filters:
        return self.doc_ids.copy()
    
    candidate_sets = []
    
    # 分类过滤
    if 'category' in filters:
        candidate_sets.append(set(self.category_index[filters['category']]))
    
    # 来源过滤
    if 'source' in filters:
        candidate_sets.append(set(self.source_index[filters['source']]))
    
    # 时间过滤
    if 'date' in filters:
        candidate_sets.append(set(self.time_index[filters['date']]))
    
    # 质量分数过滤
    if 'min_quality' in filters:
        quality_candidates = [
            doc_id for doc_id in self.doc_ids
            if self.metadata.get(doc_id, {}).get('quality_score', 0) >= filters['min_quality']
        ]
        candidate_sets.append(set(quality_candidates))
    
    # 取交集
    if candidate_sets:
        result_set = candidate_sets[0]
        for candidate_set in candidate_sets[1:]:
            result_set = result_set.intersection(candidate_set)
        return list(result_set)
    
    return self.doc_ids.copy()
```

---

## 📁 存储结构

### 目录结构

```
vector_db/                          # 数据库根目录
├── vectors.pkl                     # 向量数据（Pickle格式）
├── indexes.pkl                     # 索引数据（Pickle格式）
├── metadata.db                     # 元数据（SQLite数据库）
└── stats.json                      # 统计信息（JSON格式）
```

### 文件详情

#### 1. vectors.pkl
```python
{
    'vectors': {                     # 向量存储
        'doc_id_1': numpy.array([...]),  # 768维向量
        'doc_id_2': numpy.array([...]),
        ...
    },
    'doc_ids': ['doc_id_1', 'doc_id_2', ...],  # 文档ID列表
    'vector_dim': 768                # 向量维度
}
```

#### 2. indexes.pkl
```python
{
    'category_index': {              # 分类索引
        '科技': ['doc_1', 'doc_2'],
        '体育': ['doc_3', 'doc_4'],
        ...
    },
    'source_index': {                # 来源索引
        '新浪新闻': ['doc_1', 'doc_3'],
        '网易新闻': ['doc_2', 'doc_4'],
        ...
    },
    'time_index': {                  # 时间索引
        '2025-01-01': ['doc_1', 'doc_2'],
        '2025-01-02': ['doc_3', 'doc_4'],
        ...
    }
}
```

#### 3. metadata.db (SQLite)
```sql
-- 文档表
CREATE TABLE documents (
    doc_id TEXT PRIMARY KEY,
    title TEXT,
    content TEXT,
    category TEXT,
    source TEXT,
    publish_time TEXT,
    quality_score REAL,
    created_at TEXT,
    updated_at TEXT,
    metadata_json TEXT
);

-- 索引
CREATE INDEX idx_category ON documents(category);
CREATE INDEX idx_source ON documents(source);
CREATE INDEX idx_publish_time ON documents(publish_time);
CREATE INDEX idx_quality_score ON documents(quality_score);

-- 统计表
CREATE TABLE stats (
    key TEXT PRIMARY KEY,
    value TEXT,
    updated_at TEXT
);
```

#### 4. stats.json
```json
{
    "total_documents": 1500,
    "total_searches": 2340,
    "last_updated": "2025-01-01T12:00:00",
    "created_at": "2025-01-01T10:00:00",
    "vector_dimension": 768,
    "storage_path": "vector_db",
    "categories": {
        "科技": 450,
        "体育": 320,
        "娱乐": 280
    },
    "sources": {
        "新浪新闻": 800,
        "网易新闻": 700
    },
    "avg_quality_score": 0.75,
    "memory_usage_mb": 125.6
}
```

---

## 📝 使用示例

### 1. 基础使用

```python
from vector_database import VectorDatabase
from config import get_embedding_config

# 1. 初始化数据库
db = VectorDatabase("my_news_vectors", vector_dim=768)

# 2. 添加文档
import numpy as np
vector = np.random.rand(768)  # 实际使用中应该是API生成的向量
metadata = {
    'title': '人工智能发展新突破',
    'content': '最新研究显示...',
    'category': '科技',
    'source': '科技日报',
    'quality_score': 0.9
}

success = db.add_document("ai_news_001", vector, metadata)
print(f"添加结果: {success}")

# 3. 搜索相似文档
query_vector = np.random.rand(768)
results = db.search(query_vector, top_k=5, threshold=0.5)

for result in results:
    print(f"文档ID: {result['doc_id']}")
    print(f"相似度: {result['similarity']:.3f}")
    print(f"标题: {result['metadata']['title']}")
    print("-" * 50)

# 4. 保存数据库
db.save_database()
```

### 2. CSV批量处理

```python
from csv_to_vector_processor import CSVNewsVectorProcessor
from config import get_embedding_config

# 1. 初始化处理器
embedding_config = get_embedding_config()
processor = CSVNewsVectorProcessor(embedding_config, "news_vectors_db")

# 2. 处理CSV文件
processor.process_csv_to_vectors(
    csv_file_path="news_data.csv",
    max_workers=3,      # 3个线程并发
    batch_size=10       # 每批10条新闻
)

# 3. 查看处理结果
print("处理统计:")
print(f"  总处理: {processor.stats['total_processed']}")
print(f"  成功向量化: {processor.stats['successful_vectorized']}")
print(f"  失败: {processor.stats['failed_vectorized']}")
print(f"  跳过低质量: {processor.stats['skipped_low_quality']}")
```

### 3. 高级搜索

```python
# 1. 多条件过滤搜索
results = db.search(
    query_vector=query_vector,
    top_k=20,
    threshold=0.6,
    filters={
        'category': '科技',
        'min_quality': 0.8,
        'date': '2025-01-01'
    }
)

# 2. 分类统计
stats = db.get_stats()
print("分类分布:")
for category, count in stats['categories'].items():
    print(f"  {category}: {count} 篇")

# 3. 数据库维护
db.print_stats()  # 打印详细统计信息
```

---

## ⚡ 性能优化

### 1. 内存优化

```python
# 向量数据使用float32减少内存占用
vector = vector.astype(np.float32)

# 估算内存使用量
def _estimate_memory_usage(self) -> float:
    vector_size = len(self.vectors) * self.vector_dim * 4  # float32
    metadata_size = sum(len(str(meta)) for meta in self.metadata.values())
    return (vector_size + metadata_size) / (1024 * 1024)  # MB
```

### 2. 搜索优化

```python
# 预先归一化所有向量，加速余弦相似度计算
norm = np.linalg.norm(vector)
if norm > 0:
    vector = vector / norm

# 使用多维索引减少搜索候选集
candidate_ids = self._get_filtered_candidates(filters)
```

### 3. 并发优化

```python
# 线程安全的API调用
with self.request_lock:
    self._rate_limit_wait()
    response = self.session.post(url, headers=headers, json=data)

# 批量操作减少API调用次数
embeddings = self.get_embeddings(texts_batch)  # 一次处理多个文本
```

### 4. 存储优化

```python
# 定期保存避免数据丢失
if len(self.doc_ids) % 1000 == 0:
    self.save_database()

# 使用SQLite索引加速元数据查询
CREATE INDEX idx_category ON documents(category);
CREATE INDEX idx_quality_score ON documents(quality_score);
```

---

## 🔧 故障排除

### 1. 常见错误

#### API调用失败
```python
# 错误信息
❌ HTTP错误: 401 Client Error: Unauthorized
📄 响应状态码: 401
📄 响应内容: {"error": "Invalid API key"}

# 解决方案
1. 检查.env文件中的API密钥是否正确
2. 确认项目ID和EasyLLM ID是否匹配
3. 验证API密钥是否有足够权限
```

#### 向量维度不匹配
```python
# 错误信息
ValueError: 向量维度不匹配，期望768，实际512

# 解决方案
1. 检查embedding_config中的dimensions设置
2. 确认API返回的向量维度
3. 重新初始化数据库或转换向量维度
```

#### 内存不足
```python
# 错误信息
MemoryError: Unable to allocate array

# 解决方案
1. 减少batch_size大小
2. 降低max_workers并发数
3. 定期清理不需要的向量数据
4. 使用分批处理大文件
```

### 2. 性能问题

#### 搜索速度慢
```python
# 优化策略
1. 使用过滤条件减少候选集
2. 提高相似度阈值
3. 减少top_k数量
4. 预先归一化向量
```

#### API调用频率限制
```python
# 解决方案
1. 增加rate_limit_wait时间间隔
2. 减少并发线程数
3. 使用更大的batch_size
4. 申请更高的API配额
```

### 3. 数据问题

#### 数据质量低
```python
# 改进策略
1. 提高quality_score阈值
2. 改进内容清洗逻辑
3. 增加内容长度要求
4. 过滤重复内容
```

#### 向量化失败率高
```python
# 排查步骤
1. 检查文本长度是否超限（8000字符）
2. 验证文本编码是否正确
3. 确认API服务状态
4. 检查网络连接稳定性
```

---

*最后更新: 2025年7月8日*
*文档版本: v1.0*
*适用系统: 新闻处理向量化数据库*
